/**
 * 用戶管理頁面 JavaScript 邏輯
 * 負責用戶的 CRUD 操作和 Supabase 整合
 */

import {
  fetchAllUsers,
  updateUserRole,
  fetchUserDetails,
  authenticateAsAdmin,
  isCurrentUserAdmin,
  getCurrentAdminUser,
  initializeSupabase
} from '../../services/supabaseService.js';
import { formatDateTime, formatDate } from '../../utils/dateUtils.js';
import { $, $$, createElement, setContent, addClass, removeClass, on, ready, show, hide } from '../../utils/domUtils.js';

// 頁面狀態
let currentPage = 1;
const pageSize = 20;
let totalCount = 0;
let selectedUsers = new Set();
let filters = {};
let users = [];

// DOM 元素
let usersTableBody;
let paginationContainer;
let filterForm;
let userDetailsModal;

/**
 * 初始化頁面
 */
async function init() {
  try {
    // 初始化 Supabase
    initializeSupabase();

    // 檢查管理員身份驗證
    if (!isCurrentUserAdmin()) {
      console.log('No admin authentication found, authenticating...');
      const authSuccess = await authenticateAsAdmin();
      if (!authSuccess) {
        showToast('管理員身份驗證失敗', 'error');
        return;
      }
      showToast('管理員身份驗證成功', 'success');
    }

    // 初始化 DOM 元素
    initializeDOMElements();
    
    // 載入用戶列表
    await loadUsers();
    
    // 設置事件監聽器
    setupEventListeners();
    
    console.log('User management page initialized successfully');
  } catch (error) {
    console.error('Error initializing user management:', error);
    showToast('初始化失敗：' + error.message, 'error');
  }
}

/**
 * 初始化 DOM 元素
 */
function initializeDOMElements() {
  usersTableBody = $('#usersTableBody');
  paginationContainer = $('#paginationContainer');
  filterForm = $('#filterForm');
  userDetailsModal = $('#userDetailsModal');
}

/**
 * 載入用戶列表
 */
async function loadUsers() {
  try {
    showLoading(true);
    
    const result = await fetchAllUsers(filters, { page: currentPage, limit: pageSize });
    users = result.users;
    totalCount = result.totalCount;
    
    renderUsersTable();
    renderPagination();
    updateUserCount();
    
  } catch (error) {
    console.error('Error loading users:', error);
    showToast('載入用戶列表失敗：' + error.message, 'error');
  } finally {
    showLoading(false);
  }
}

/**
 * 渲染用戶表格
 */
function renderUsersTable() {
  if (!usersTableBody) return;

  if (users.length === 0) {
    usersTableBody.innerHTML = `
      <tr>
        <td colspan="6" class="no-data">
          <div class="no-data-message">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
              <circle cx="12" cy="7" r="4"/>
            </svg>
            <p>目前沒有用戶資料</p>
          </div>
        </td>
      </tr>
    `;
    return;
  }

  const rows = users.map(user => `
    <tr>
      <td>
        <input type="checkbox" class="user-checkbox" value="${user.id}" 
               ${selectedUsers.has(user.id) ? 'checked' : ''}>
      </td>
      <td>
        <div class="user-info">
          <div class="user-avatar">
            <img src="${user.picture_url || '/api/placeholder/40/40'}" alt="用戶頭像" onerror="this.src='/api/placeholder/40/40'">
          </div>
          <div class="user-details">
            <div class="user-display-name">
              <div class="community-nickname">${escapeHtml(user.community_nickname || user.display_name || '未設定')}</div>
              <div class="line-name">LINE: ${escapeHtml(user.display_name || '未知')}</div>
            </div>
          </div>
        </div>
      </td>
      <td>
        <div class="user-id">${escapeHtml(user.id)}</div>
      </td>
      <td>
        <span class="user-role role-${user.role || 'user'}">
          ${getUserRoleText(user.role)}
        </span>
      </td>
      <td>${user.last_login_at ? formatDateTime(user.last_login_at) : '從未登入'}</td>
      <td>
        <div class="action-buttons">
          <button class="action-btn view-btn" onclick="viewUserDetails('${user.id}')" title="查看詳情">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
              <circle cx="12" cy="12" r="3"/>
            </svg>
          </button>
          <button class="action-btn edit-btn" onclick="editUserRole('${user.id}', '${user.role || 'user'}')" title="編輯角色">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
            </svg>
          </button>
        </div>
      </td>
    </tr>
  `).join('');

  usersTableBody.innerHTML = rows;
}

/**
 * 渲染分頁
 */
function renderPagination() {
  if (!paginationContainer) return;

  const totalPages = Math.ceil(totalCount / pageSize);
  if (totalPages <= 1) {
    paginationContainer.innerHTML = '';
    return;
  }

  let paginationHTML = '<div class="pagination">';
  
  // 上一頁
  if (currentPage > 1) {
    paginationHTML += `<button class="pagination-btn" onclick="changePage(${currentPage - 1})">上一頁</button>`;
  }
  
  // 頁碼
  for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
    paginationHTML += `<button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">${i}</button>`;
  }
  
  // 下一頁
  if (currentPage < totalPages) {
    paginationHTML += `<button class="pagination-btn" onclick="changePage(${currentPage + 1})">下一頁</button>`;
  }
  
  paginationHTML += '</div>';
  paginationContainer.innerHTML = paginationHTML;
}

/**
 * 更新用戶數量顯示
 */
function updateUserCount() {
  const countElement = $('#userCount');
  if (countElement) {
    setContent(countElement, `共 ${totalCount} 個用戶`);
  }
}

/**
 * 設置事件監聽器
 */
function setupEventListeners() {
  // 全選/取消全選
  const selectAllCheckbox = $('#selectAllUsers');
  if (selectAllCheckbox) {
    on(selectAllCheckbox, 'change', handleSelectAll);
  }

  // 篩選表單
  if (filterForm) {
    on(filterForm, 'submit', handleFilterSubmit);
  }

  // 重置篩選
  const resetFilterBtn = $('#resetFilterBtn');
  if (resetFilterBtn) {
    on(resetFilterBtn, 'click', resetFilters);
  }
}

/**
 * 處理全選/取消全選
 */
function handleSelectAll(event) {
  const isChecked = event.target.checked;
  const checkboxes = $$('.user-checkbox');
  
  checkboxes.forEach(checkbox => {
    checkbox.checked = isChecked;
    if (isChecked) {
      selectedUsers.add(checkbox.value);
    } else {
      selectedUsers.delete(checkbox.value);
    }
  });
}

/**
 * 處理篩選表單提交
 */
function handleFilterSubmit(event) {
  event.preventDefault();
  
  const formData = new FormData(event.target);
  filters = {
    userId: formData.get('userId') || null,
    displayName: formData.get('displayName') || null,
    role: formData.get('role') || null,
    startDate: formData.get('startDate') || null,
    endDate: formData.get('endDate') || null
  };
  
  currentPage = 1;
  loadUsers();
}

/**
 * 重置篩選條件
 */
function resetFilters() {
  filters = {};
  currentPage = 1;
  
  if (filterForm) {
    filterForm.reset();
  }
  
  loadUsers();
}

/**
 * 顯示載入狀態
 */
function showLoading(show) {
  const loadingElement = $('#loadingIndicator');
  if (loadingElement) {
    if (show) {
      removeClass(loadingElement, 'hidden');
    } else {
      addClass(loadingElement, 'hidden');
    }
  }
}

/**
 * 顯示 Toast 通知
 */
function showToast(message, type = 'info') {
  // 簡單的 toast 實現
  const toast = createElement('div', {
    className: `toast toast-${type}`,
    textContent: message
  });
  
  document.body.appendChild(toast);
  
  setTimeout(() => {
    addClass(toast, 'show');
  }, 100);
  
  setTimeout(() => {
    removeClass(toast, 'show');
    setTimeout(() => {
      document.body.removeChild(toast);
    }, 300);
  }, 3000);
}

/**
 * HTML 轉義
 */
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * 獲取用戶角色文字
 */
function getUserRoleText(role) {
  switch (role) {
    case 'admin':
      return '系統管理員';
    case 'manager':
      return '管理員';
    case 'user':
    default:
      return '一般用戶';
  }
}

// 將函數暴露到全局作用域供 HTML 調用
window.changePage = function(page) {
  currentPage = page;
  loadUsers();
};

window.viewUserDetails = function(userId) {
  console.log('View user details:', userId);
  showToast('用戶詳情功能開發中', 'info');
};

window.editUserRole = function(userId, currentRole) {
  const newRole = prompt(`請輸入新角色 (admin/manager/user)，當前角色：${currentRole}`, currentRole);
  if (newRole && newRole !== currentRole) {
    updateUserRole(userId, newRole).then(() => {
      showToast('用戶角色更新成功', 'success');
      loadUsers();
    }).catch(error => {
      console.error('Error updating user role:', error);
      showToast('更新用戶角色失敗：' + error.message, 'error');
    });
  }
};

// 頁面載入完成後初始化
ready(init);
