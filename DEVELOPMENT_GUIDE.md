# 🛠️ 小森活預購系統 - 開發指引

這是一份針對 LLM（大型語言模型）的詳細開發指引，旨在協助完成「小森活預購系統」的重構和開發。

## 🎯 專案目標

快速重構現有的「小森活預購系統」。新系統採用混合架構：

- **使用者前端 (User Interface)**：位於 `public/app/user/`，基於 HTML、CSS 和 Vanilla JavaScript
- **管理後台 (Admin Panel)**：位於 `public/app/admin/`，同樣基於獨立的 HTML、CSS 和 Vanilla JavaScript
- **核心交互與狀態管理**：輕量級客戶端狀態管理和模組化 JavaScript
- **後端服務**：Supabase (資料庫、RPC 函數、觸發器、RLS)
- **圖片儲存**：Cloudinary
- **開發與建置工具**：Vite

## 📁 核心參考文件

這些文件是理解系統架構、資料庫和業務邏輯的**最重要依據**：

1. **`previous_ref/design-readme.md`**: 完整的資料庫表結構、欄位說明、業務規則、核心功能描述
2. **`previous_ref/schema_only.sql`**: Supabase 資料庫結構的 SQL 匯出
3. **`previous_ref/supabase-rpc-triggers.sql`**: 所有後端 Supabase 的 RPC 函數和觸發器定義
4. **`previous_ref/row-level-security.md`**: Supabase 資料表的行級安全 (RLS) 策略
5. **`previous_ref/database-triggers.md`**: 專案狀態自動更新等資料庫觸發器邏輯

## 🏗️ JavaScript 模組化架構

### 建議的目錄結構
```
public/app/js/
├── store/              // 狀態管理
│   ├── cartStore.js    // 管理購物車狀態
│   └── authStore.js    // 管理使用者認證狀態
├── services/           // 封裝與外部服務的互動
│   ├── supabaseService.js // Supabase 資料庫操作
│   ├── cloudinaryService.js // 圖片上傳邏輯
│   └── liffService.js    // LIFF SDK 操作
├── pages/              // 各 HTML 頁面對應的主要邏輯
│   ├── user/           // 使用者前端頁面的 JS
│   │   ├── home.js
│   │   ├── preorder.js
│   │   └── orderHistory.js
│   └── admin/          // 管理後台頁面的 JS
│       ├── dashboard.js
│       ├── projectManagement.js
│       └── orderManagement.js
├── utils/              // 通用工具函式
│   ├── dateUtils.js
│   ├── discountCalculator.js
│   └── domUtils.js
└── main.js             // 全局初始化腳本
```

### 模組化要求
- 所有 JavaScript 檔案都應使用 ES6 模組 (`import`/`export`)
- 在 HTML 檔案中使用 `<script type="module" src="path/to/module.js"></script>` 引入
- 避免使用全域變數，優先使用模組作用域和狀態管理器

## 🔄 狀態管理

### 目標
解決各 HTML 頁面間複雜的交互和共享狀態問題。

### Store 結構設計
- **`cartStore.js`**: 購物車內的商品列表、加入商品、更新數量、移除商品、清空購物車、計算總商品數、計算小計等
- **`authStore.js`**: 目前使用者資訊、登入狀態、登入/登出方法等

### 使用方式
- 在各個 `pages/*.js` 中 `import` store
- 讀取 store 狀態 (e.g., `cartItems.get()`)
- 調用 store 的 action 來更新狀態 (e.g., `addItem({id: '123', ...})`)
- 訂閱 store 的變化 (`cartItems.subscribe(newItems => { /* 更新 DOM */ })`)

## 🗄️ Supabase 服務層

### 目標
提供一個集中的介面來與 Supabase 互動。

### 主要任務
- **初始化 Client**: 使用環境變數初始化 Supabase JavaScript Client
- **移植和改寫函式**: 從 `previous_ref/supabase.ts` 中提取核心邏輯，改寫成 Vanilla JS
- **錯誤處理**: 統一處理 Supabase API 返回的錯誤
- **RLS 合規**: 確保所有資料庫查詢都符合 RLS 策略

### 重要函數
- `fetchActiveProjects`
- `fetchProjectDiscounts`
- `createOrderWithItems`
- `fetchUserOrders`
- `generateDisplayIdByRPC`

## 🖼️ Cloudinary 服務層

### 目標
處理圖片上傳到 Cloudinary。

### 主要功能
- 參考 `previous_ref/cloudinary.ts` 初始化 Cloudinary
- 實現 `uploadImage(file)` 函式
- 使用 `fetch` API 和 `FormData` 進行上傳
- 返回圖片的 URL

## 📱 LIFF 服務層

### 目標
封裝 LIFF SDK 的初始化和常用操作。

### 主要功能
- **初始化 LIFF**: 使用環境變數中的 `VITE_LIFF_ID`
- **登入/登出**: 封裝 `liff.login()` 和 `liff.logout()`
- **獲取用戶資料**: 封裝 `liff.getProfile()`，存儲到 `authStore.js`
- **檢查登入狀態**: 封裝 `liff.isLoggedIn()`

## 📄 頁面邏輯實現

### 目標
為每個 HTML 頁面編寫對應的 JavaScript 邏輯。

### 通用任務
- **DOM 元素選擇**: 使用 `document.getElementById`, `document.querySelector`
- **事件監聽**: 使用 `element.addEventListener`
- **DOM 更新**: 手動操作 DOM
- **狀態管理**: 從 `store/*.js` 讀取和更新共享狀態
- **數據獲取**: 從 `services/*.js` 獲取數據

### 具體頁面範例

#### `user/home.js`
- 呼叫 `supabaseService.fetchActiveProjects()` 獲取專案列表
- 動態渲染專案卡片
- 處理搜尋和篩選邏輯

#### `user/preorder.js`
- 根據 URL 參數獲取專案詳情和商品列表
- 渲染商品卡片
- 處理商品數量增減，更新 `cartStore`
- 計算折扣和總價
- 處理訂單提交

#### `admin/projectManagement.js`
- 獲取專案列表並渲染到表格
- 實現新增/編輯專案的表單邏輯
- 處理刪除專案
- 處理圖片上傳

## 🎨 樣式與視覺

- 主要依賴 `public/app/shared-styles.css` 和各頁面特定 CSS
- 確保 Vanilla JS 在更新 DOM 時能正確應用樣式
- 視覺效果參考 `previous_ref/visual-guide.html`

## ⚠️ 開發注意事項

### 逐步進行
先完成基礎建設（JS 模組化、服務層初始化），然後逐個頁面實現核心功能。

### 錯誤處理
在所有 API 呼叫和關鍵邏輯中加入適當的錯誤處理。

### 使用者體驗
考慮互動的流暢性和響應性（例如，操作時顯示 loading 狀態）。

### 安全性
所有 Supabase 操作都必須通過後端的 RLS 和 RPC 函數進行權限控制。

### 日誌
在關鍵步驟使用 `console.log` 進行調試。

## 🔧 開發工具

### 環境設置
- Node.js 18+
- Vite 開發服務器
- Supabase CLI（可選）

### 調試工具
- 瀏覽器開發者工具
- Supabase Dashboard
- 網路請求監控

### 測試方法
- 手動功能測試
- 跨瀏覽器兼容性測試
- 移動端響應式測試

---

這份指引提供了完整的開發框架和最佳實踐，確保系統的可維護性和擴展性。
