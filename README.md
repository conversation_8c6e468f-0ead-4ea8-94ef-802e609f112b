# 🌲 小森活預購系統

一個基於 LINE LIFF 的預購團購系統，支援多專案管理、訂單處理和用戶管理。

## 🎯 專案狀態

**當前版本**: v1.0.0-beta
**開發狀態**: 核心功能已完成，Supabase 資料整合完成
**部署狀態**: 開發環境運行中

### ✅ 已完成功能
- 🔐 管理員身份驗證系統
- 📊 Supabase 資料庫完整整合
- 🏠 用戶端首頁（真實專案資料）
- 🛒 預購頁面（真實商品資料）
- 📋 訂單歷史（真實訂單資料）
- 🎛️ 管理員儀表板（真實統計資料）
- 📦 訂單管理系統（完整 CRUD）

### 🚧 進行中功能
- 👥 用戶管理頁面
- 📁 專案管理頁面
- 🔔 通知系統

## 🏗️ 技術架構

### 前端架構
- **框架**: Vanilla JavaScript (ES6+) + HTML5 + CSS3
- **模組化**: ES6 Modules
- **狀態管理**: 自定義 Store 模式
- **UI 組件**: 原生 DOM 操作 + 工具函數

### 後端架構
- **資料庫**: Supabase PostgreSQL
- **身份驗證**: 自定義管理員驗證 + LINE LIFF
- **API**: Supabase REST API + RPC 函數
- **權限控制**: Row Level Security (RLS)

### 整合服務
- **LINE 平台**: LIFF SDK v2
- **部署平台**: Vercel
- **開發工具**: Vite

## 🔐 權限架構設計

### RLS (Row Level Security) 分層
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   匿名用戶      │───▶│  RPC 函數        │───▶│   公開資料      │
│   (瀏覽專案)    │    │  (SECURITY       │    │   (活躍專案)    │
│                 │    │   DEFINER)       │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘

┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   一般用戶      │───▶│  身份驗證 +      │───▶│   個人資料      │
│   (查看訂單)    │    │  RPC 函數        │    │   (個人訂單)    │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘

┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   管理員        │───▶│  管理員驗證 +    │───▶│   全部資料      │
│   (系統管理)    │    │  Admin RPC       │    │   (所有訂單)    │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 已實現的 RPC 函數
- `get_active_projects()` - 獲取活躍專案（公開）
- `get_project_with_items(project_id)` - 獲取專案及商品（公開）
- `get_project_discounts(project_id)` - 獲取專案折扣（公開）
- `get_user_orders(user_id)` - 獲取用戶訂單（需身份驗證）
- `admin_authenticate(admin_id)` - 管理員身份驗證
- `admin_get_all_orders(...)` - 管理員查詢所有訂單
- `admin_get_order_details(order_id)` - 管理員查詢訂單詳情

## 🚀 快速開始

### 環境需求
- Node.js 18+
- npm 或 yarn
- Supabase 帳號

### 開發環境設置

1. **克隆專案**
```bash
git clone <repository-url>
cd small-forest-life-mix
```

2. **安裝依賴**
```bash
npm install
```

3. **環境變數設定**
```bash
# 複製環境變數範本
cp .env.example .env.local

# 編輯 .env.local，填入以下資訊：
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_LIFF_ID=your_liff_id
```

4. **啟動開發服務器**
```bash
npm run dev
```

5. **訪問應用**
- 用戶端：`http://localhost:3002/app/user/index.html`
- 管理端：`http://localhost:3002/app/admin/admin-login.html`

### 管理員登入
- 管理員 ID: `admin_test_user`
- 系統會自動驗證並導向管理後台

## 📁 專案結構

```
small-forest-life-mix/
├── public/
│   └── app/
│       ├── user/                    # 用戶端頁面
│       │   ├── index.html          # 首頁
│       │   ├── preorder.html       # 預購頁面
│       │   └── order-history.html  # 訂單歷史
│       ├── admin/                   # 管理員頁面
│       │   ├── admin-login.html    # 管理員登入
│       │   ├── dashboard.html      # 儀表板
│       │   ├── auth-test.html      # 身份驗證測試
│       │   └── management/         # 管理功能
│       │       └── order-management.html
│       └── js/                      # JavaScript 模組
│           ├── services/           # 服務層
│           │   ├── supabaseService.js  # 資料庫服務
│           │   └── liffService.js      # LINE LIFF 服務
│           ├── utils/              # 工具函數
│           ├── store/              # 狀態管理
│           └── pages/              # 頁面邏輯
├── docs/                           # 文件
├── INTEGRATION_PROGRESS.md        # 整合進度
└── README.md                      # 專案說明
```

## 🔧 開發指南

### 新增頁面流程
1. 在對應目錄創建 HTML 文件
2. 在 `js/pages/` 創建對應的 JavaScript 邏輯
3. 如需資料庫操作，在 `supabaseService.js` 添加函數
4. 如需 RLS 繞過，創建對應的 RPC 函數

### 資料庫操作原則
- 公開資料：使用 RPC 函數繞過 RLS
- 用戶資料：需要身份驗證
- 管理員資料：需要管理員身份驗證
- 所有 RPC 函數使用 `SECURITY DEFINER`

### 測試頁面
- 身份驗證測試：`/app/admin/auth-test.html`
- 訂單功能測試：`/app/admin/test-orders.html`

## 📋 待辦事項

### 🔥 高優先級
- [ ] 完成專案管理頁面 (`/app/admin/management/projects.html`)
- [ ] 完成用戶管理頁面 (`/app/admin/management/user-management.html`)
- [ ] 實現訂單狀態更新通知
- [ ] 添加訂單詳情彈窗

### 🔄 中優先級
-
- [ ] 添加專案圖片上傳功能
- [ ] 實現購物車功能
- [ ] 添加訂單匯出功能

### 📈 低優先級
- [ ] 添加統計圖表
- [ ] 實現 LINE LIFF 用戶身份驗證


## 🚀 部署

### Vercel 部署
專案已設定自動部署：
1. 推送到 `main` 分支
2. Vercel 自動建置和部署
3. 環境變數需在 Vercel 後台設定

### 環境變數設定
在 Vercel 後台設定以下環境變數：
- `VITE_SUPABASE_URL`
- `VITE_SUPABASE_ANON_KEY`
- `VITE_LIFF_ID`

## 🤝 貢獻指南

1. Fork 專案
2. 創建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 遵循現有的程式碼風格
4. 更新相關文件
5. 提交變更 (`git commit -m 'Add some AmazingFeature'`)
6. 推送到分支 (`git push origin feature/AmazingFeature`)
7. 開啟 Pull Request

## 📞 支援

如有問題請查看：
- [整合進度文件](INTEGRATION_PROGRESS.md)
- [測試頁面](http://localhost:3002/app/admin/auth-test.html)

## 📄 授權

此專案採用 MIT 授權 - 詳見 [LICENSE](LICENSE) 文件


