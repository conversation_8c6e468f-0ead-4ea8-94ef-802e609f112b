/**
 * 專案管理頁面 JavaScript 邏輯
 * 負責專案的 CRUD 操作和 Supabase 整合
 */

import {
  fetchAllProjectsAdmin,
  createProject,
  updateProject,
  deleteProjects,
  authenticateAsAdmin,
  isCurrentUserAdmin,
  getCurrentAdminUser,
  initializeSupabase
} from '../../services/supabaseService.js';
import {
  uploadImage,
  uploadMultipleImages,
  getThumbnailUrl,
  validateImageFile
} from '../../services/cloudinaryService.js';
import { formatDateTime, formatDate } from '../../utils/dateUtils.js';
import { $, $$, createElement, setContent, addClass, removeClass, on, ready, show, hide } from '../../utils/domUtils.js';

/**
 * 顯示 Toast 通知
 */
function showToast(message, type = 'info') {
  // 創建 toast 元素
  const toast = createElement('div', {
    className: `toast toast-${type}`,
    textContent: message
  });

  // 添加到頁面
  document.body.appendChild(toast);

  // 顯示動畫
  setTimeout(() => addClass(toast, 'show'), 100);

  // 自動移除
  setTimeout(() => {
    removeClass(toast, 'show');
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 300);
  }, 3000);
}

/**
 * 顯示/隱藏載入指示器
 */
function showLoading(show = true) {
  const loadingIndicator = $('#loadingIndicator');
  if (loadingIndicator) {
    if (show) {
      removeClass(loadingIndicator, 'hidden');
    } else {
      addClass(loadingIndicator, 'hidden');
    }
  }
}

// 頁面狀態
let currentPage = 1;
const pageSize = 20;
let totalCount = 0;
let selectedProjects = new Set();
let filters = {};
let projects = [];
let currentEditingProjectId = null;
let uploadedImages = []; // 儲存已上傳的圖片 URL

// DOM 元素
let projectsTableBody;
let paginationContainer;
let filterForm;
let addProjectModal;
let addProjectForm;

/**
 * 初始化頁面
 */
async function init() {
  try {
    // 初始化 Supabase
    initializeSupabase();

    // 檢查管理員身份驗證
    if (!isCurrentUserAdmin()) {
      console.log('No admin authentication found, authenticating...');
      const authSuccess = await authenticateAsAdmin();
      if (!authSuccess) {
        showToast('管理員身份驗證失敗', 'error');
        return;
      }
      showToast('管理員身份驗證成功', 'success');
    }

    // 初始化 DOM 元素
    initializeDOMElements();

    // 載入專案列表
    await loadProjects();

    // 設置事件監聽器
    setupEventListeners();

    console.log('Project management page initialized successfully');
  } catch (error) {
    console.error('Error initializing project management:', error);
    showToast('初始化失敗：' + error.message, 'error');
  }
}

/**
 * 初始化 DOM 元素
 */
function initializeDOMElements() {
  projectsTableBody = $('#projectsTableBody');
  paginationContainer = $('#paginationContainer');
  filterForm = $('#filterForm');
  addProjectModal = $('#addProjectModal');
  addProjectForm = $('#addProjectForm');
}

/**
 * 載入專案列表
 */
async function loadProjects() {
  try {
    showLoading(true);

    const result = await fetchAllProjectsAdmin(filters, { page: currentPage, limit: pageSize });
    projects = result.projects;
    totalCount = result.totalCount;

    renderProjectsTable();
    renderPagination();
    updateProjectCount();

  } catch (error) {
    console.error('Error loading projects:', error);
    showToast('載入專案列表失敗：' + error.message, 'error');
  } finally {
    showLoading(false);
  }
}

/**
 * 渲染專案表格
 */
function renderProjectsTable() {
  if (!projectsTableBody) return;

  if (projects.length === 0) {
    projectsTableBody.innerHTML = `
      <tr>
        <td colspan="7" class="no-data">
          <div class="no-data-message">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
              <path d="M9 12l2 2 4-4"/>
              <path d="M21 12c.552 0 1-.448 1-1V5c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h18z"/>
            </svg>
            <p>目前沒有專案資料</p>
          </div>
        </td>
      </tr>
    `;
    return;
  }

  const rows = projects.map(project => `
    <tr>
      <td>
        <input type="checkbox" class="project-checkbox" value="${project.id}"
               ${selectedProjects.has(project.id) ? 'checked' : ''}>
      </td>
      <td>
        <div class="project-info">
          <div class="project-name">${escapeHtml(project.name)}</div>
          <div class="project-id">#${project.project_id_display || project.id}</div>
        </div>
      </td>
      <td>
        <span class="project-status status-${project.project_status}">
          ${getProjectStatusText(project.project_status)}
        </span>
      </td>
      <td>${project.items_count || 0} 項商品</td>
      <td>${formatDate(project.created_at)}</td>
      <td>${formatDate(project.updated_at)}</td>
      <td>
        <div class="action-buttons">
          <button class="action-btn edit-btn" onclick="editProject('${project.id}')" title="編輯">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
            </svg>
          </button>
          <button class="action-btn delete-btn" onclick="deleteProject('${project.id}')" title="刪除">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="3,6 5,6 21,6"/>
              <path d="M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
            </svg>
          </button>
        </div>
      </td>
    </tr>
  `).join('');

  projectsTableBody.innerHTML = rows;

  // 添加複選框事件監聽器
  const checkboxes = $$('.project-checkbox');
  checkboxes.forEach(checkbox => {
    on(checkbox, 'change', handleProjectCheckboxChange);
  });
}

/**
 * 渲染分頁
 */
function renderPagination() {
  if (!paginationContainer) return;

  const totalPages = Math.ceil(totalCount / pageSize);
  if (totalPages <= 1) {
    paginationContainer.innerHTML = '';
    return;
  }

  let paginationHTML = '<div class="pagination">';

  // 上一頁
  if (currentPage > 1) {
    paginationHTML += `<button class="pagination-btn" onclick="changePage(${currentPage - 1})">上一頁</button>`;
  }

  // 頁碼
  for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
    paginationHTML += `<button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">${i}</button>`;
  }

  // 下一頁
  if (currentPage < totalPages) {
    paginationHTML += `<button class="pagination-btn" onclick="changePage(${currentPage + 1})">下一頁</button>`;
  }

  paginationHTML += '</div>';
  paginationContainer.innerHTML = paginationHTML;
}

/**
 * 更新專案數量顯示
 */
function updateProjectCount() {
  const countElement = $('#projectCount');
  if (countElement) {
    setContent(countElement, `共 ${totalCount} 個專案`);
  }
}

/**
 * 設置事件監聽器
 */
function setupEventListeners() {
  // 全選/取消全選
  const selectAllCheckbox = $('#selectAllProjects');
  if (selectAllCheckbox) {
    on(selectAllCheckbox, 'change', handleSelectAll);
  }

  // 篩選表單
  if (filterForm) {
    on(filterForm, 'submit', handleFilterSubmit);
  }

  // 重置篩選
  const resetFilterBtn = $('#resetFilterBtn');
  if (resetFilterBtn) {
    on(resetFilterBtn, 'click', resetFilters);
  }

  // 新增專案按鈕
  const addProjectBtn = $('#openAddProjectModal');
  if (addProjectBtn) {
    on(addProjectBtn, 'click', openAddProjectModal);
  }

  // 批量操作按鈕
  const batchDeleteBtn = $('#batchDeleteBtn');
  if (batchDeleteBtn) {
    on(batchDeleteBtn, 'click', handleBatchDelete);
  }

  // 專案表單提交
  if (addProjectForm) {
    on(addProjectForm, 'submit', handleProjectSubmit);
  }
}

/**
 * 顯示載入狀態
 */
function showLoading(show) {
  const loadingElement = $('#loadingIndicator');
  if (loadingElement) {
    if (show) {
      removeClass(loadingElement, 'hidden');
    } else {
      addClass(loadingElement, 'hidden');
    }
  }
}

/**
 * 顯示 Toast 通知
 */
function showToast(message, type = 'info') {
  // 簡單的 toast 實現
  const toast = createElement('div', {
    className: `toast toast-${type}`,
    textContent: message
  });

  document.body.appendChild(toast);

  setTimeout(() => {
    addClass(toast, 'show');
  }, 100);

  setTimeout(() => {
    removeClass(toast, 'show');
    setTimeout(() => {
      document.body.removeChild(toast);
    }, 300);
  }, 3000);
}

/**
 * HTML 轉義
 */
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * 獲取專案狀態文字
 */
function getProjectStatusText(status) {
  switch (status) {
    case 'active':
      return '進行中';
    case 'ordering_ended':
      return '結束預購';
    case 'arrived':
      return '已到貨';
    case 'completed':
      return '已結束';
    default:
      return '未知狀態';
  }
}

// 將函數暴露到全局作用域供 HTML 調用
window.changePage = function(page) {
  currentPage = page;
  loadProjects();
};

/**
 * 處理全選/取消全選
 */
function handleSelectAll(event) {
  const isChecked = event.target.checked;
  const checkboxes = $$('.project-checkbox');

  checkboxes.forEach(checkbox => {
    checkbox.checked = isChecked;
    if (isChecked) {
      selectedProjects.add(checkbox.value);
    } else {
      selectedProjects.delete(checkbox.value);
    }
  });

  updateBatchButtons();
}

/**
 * 處理篩選表單提交
 */
function handleFilterSubmit(event) {
  event.preventDefault();

  const formData = new FormData(event.target);
  filters = {
    name: formData.get('projectName') || null,
    status: formData.get('projectStatus') || null,
    startDate: formData.get('startDate') || null,
    endDate: formData.get('endDate') || null
  };

  currentPage = 1;
  loadProjects();
}

/**
 * 重置篩選條件
 */
function resetFilters() {
  filters = {};
  currentPage = 1;

  if (filterForm) {
    filterForm.reset();
  }

  loadProjects();
}

/**
 * 開啟新增專案模態框
 */
function openAddProjectModal() {
  if (addProjectModal) {
    addClass(addProjectModal, 'active');
    document.body.style.overflow = 'hidden';
  }
}

/**
 * 關閉新增專案模態框
 */
function closeAddProjectModal() {
  if (addProjectModal) {
    removeClass(addProjectModal, 'active');
    document.body.style.overflow = '';

    if (addProjectForm) {
      addProjectForm.reset();
    }

    // 重置編輯狀態
    currentEditingProjectId = null;

    // 清空圖片預覽
    clearImagePreviews();

    // 重置模態框標題
    const modalTitle = $('.modal-title');
    if (modalTitle) {
      modalTitle.textContent = '新增專案';
    }
  }
}

/**
 * 處理專案表格中的複選框變化
 */
function handleProjectCheckboxChange(event) {
  const checkbox = event.target;
  const projectId = checkbox.value;

  if (checkbox.checked) {
    selectedProjects.add(projectId);
  } else {
    selectedProjects.delete(projectId);
  }

  updateBatchButtons();
  updateSelectAllCheckbox();
}

/**
 * 更新全選複選框狀態
 */
function updateSelectAllCheckbox() {
  const selectAllCheckbox = $('#selectAllProjects');
  const projectCheckboxes = $$('.project-checkbox');

  if (!selectAllCheckbox || projectCheckboxes.length === 0) return;

  const checkedCount = projectCheckboxes.filter(cb => cb.checked).length;

  if (checkedCount === 0) {
    selectAllCheckbox.checked = false;
    selectAllCheckbox.indeterminate = false;
  } else if (checkedCount === projectCheckboxes.length) {
    selectAllCheckbox.checked = true;
    selectAllCheckbox.indeterminate = false;
  } else {
    selectAllCheckbox.checked = false;
    selectAllCheckbox.indeterminate = true;
  }
}

/**
 * 處理專案表單提交
 */
async function handleProjectSubmit(event) {
  event.preventDefault();

  try {
    // 直接從表單元素獲取值，而不是使用 FormData
    const projectName = $('#projectName')?.value;
    const projectDescription = $('#projectDescription')?.value;
    const projectStatus = $('#projectStatus')?.value;

    if (!projectName) {
      showToast('請輸入專案名稱', 'error');
      return;
    }

    const projectData = {
      name: projectName,
      description: projectDescription || '',
      status: projectStatus || 'active',
      images: uploadedImages, // 使用已上傳的圖片 URL
      highlights: [], // TODO: 處理亮點
      items: [], // TODO: 處理商品
      discounts: [] // TODO: 處理折扣
    };

    showLoading(true);

    if (currentEditingProjectId) {
      // 編輯模式
      await updateProject(currentEditingProjectId, projectData);
      showToast('專案更新成功', 'success');
    } else {
      // 創建模式
      await createProject(projectData);
      showToast('專案創建成功', 'success');
    }

    closeAddProjectModal();
    await loadProjects();

  } catch (error) {
    console.error('Error saving project:', error);
    const action = currentEditingProjectId ? '更新' : '創建';
    showToast(`${action}專案失敗：` + error.message, 'error');
  } finally {
    showLoading(false);
  }
}

/**
 * 處理批量刪除
 */
async function handleBatchDelete() {
  if (selectedProjects.size === 0) {
    showToast('請選擇要刪除的專案', 'warning');
    return;
  }

  if (!confirm(`確定要刪除選中的 ${selectedProjects.size} 個專案嗎？`)) {
    return;
  }

  try {
    showLoading(true);

    await deleteProjects(Array.from(selectedProjects));

    showToast('專案刪除成功', 'success');
    selectedProjects.clear();
    await loadProjects();

  } catch (error) {
    console.error('Error deleting projects:', error);
    showToast('刪除專案失敗：' + error.message, 'error');
  } finally {
    showLoading(false);
  }
}

/**
 * 更新批量操作按鈕狀態
 */
function updateBatchButtons() {
  const batchDeleteBtn = $('#batchDeleteBtn');
  if (batchDeleteBtn) {
    if (selectedProjects.size > 0) {
      removeClass(batchDeleteBtn, 'disabled');
      batchDeleteBtn.disabled = false;
    } else {
      addClass(batchDeleteBtn, 'disabled');
      batchDeleteBtn.disabled = true;
    }
  }
}

window.editProject = async function(projectId) {
  try {
    // 找到要編輯的專案
    const project = projects.find(p => p.id === projectId);
    if (!project) {
      showToast('找不到指定的專案', 'error');
      return;
    }

    // 填充表單
    const projectNameInput = $('#projectName');
    const projectDescriptionInput = $('#projectDescription');
    const projectStatusSelect = $('#projectStatus');

    if (projectNameInput) projectNameInput.value = project.name || '';
    if (projectDescriptionInput) projectDescriptionInput.value = project.description || '';
    if (projectStatusSelect) projectStatusSelect.value = project.project_status || 'active';

    // 更新模態框標題
    const modalTitle = $('.modal-title');
    if (modalTitle) {
      modalTitle.textContent = '編輯專案';
    }

    // 設置編輯模式
    currentEditingProjectId = projectId;

    // 打開模態框
    openAddProjectModal();

  } catch (error) {
    console.error('Error editing project:', error);
    showToast('載入專案資料失敗：' + error.message, 'error');
  }
};

window.deleteProject = function(projectId) {
  if (!confirm('確定要刪除這個專案嗎？')) {
    return;
  }

  deleteProjects([projectId]).then(() => {
    showToast('專案刪除成功', 'success');
    loadProjects();
  }).catch(error => {
    console.error('Error deleting project:', error);
    showToast('刪除專案失敗：' + error.message, 'error');
  });
};

// 將關閉模態框函數暴露到全局
window.closeAddProjectModal = closeAddProjectModal;

/**
 * 初始化圖片上傳功能
 */
function initImageUpload() {
  const uploadBtn = $('#uploadProjectImageBtn');
  const imagePreviewList = $('#imagePreviewList');

  if (uploadBtn) {
    on(uploadBtn, 'click', handleImageUploadClick);
  }

  // 創建隱藏的文件輸入
  const fileInput = createElement('input', {
    type: 'file',
    multiple: true,
    accept: 'image/*',
    style: 'display: none'
  });

  document.body.appendChild(fileInput);

  on(fileInput, 'change', handleImageFileSelect);

  // 儲存文件輸入的引用
  window.projectImageFileInput = fileInput;
}

/**
 * 處理圖片上傳按鈕點擊
 */
function handleImageUploadClick() {
  if (window.projectImageFileInput) {
    window.projectImageFileInput.click();
  }
}

/**
 * 處理圖片文件選擇
 */
async function handleImageFileSelect(event) {
  const files = event.target.files;
  if (!files || files.length === 0) return;

  try {
    showLoading(true);

    // 驗證文件
    for (let file of files) {
      const validation = validateImageFile(file);
      if (!validation.valid) {
        showToast(`文件 ${file.name} 驗證失敗：${validation.errors.join(', ')}`, 'error');
        return;
      }
    }

    // 上傳圖片
    const uploadOptions = {
      folder: 'projects', // Cloudinary 資料夾
      tags: ['project', 'admin-upload']
    };

    const results = await uploadMultipleImages(files, uploadOptions);

    // 處理上傳結果
    results.forEach((url, index) => {
      if (url) {
        uploadedImages.push(url);
        addImagePreview(url, files[index].name);
      }
    });

    showToast(`成功上傳 ${results.length} 張圖片`, 'success');

  } catch (error) {
    console.error('Error uploading images:', error);
    showToast('圖片上傳失敗：' + error.message, 'error');
  } finally {
    showLoading(false);
    // 清空文件輸入
    event.target.value = '';
  }
}

/**
 * 添加圖片預覽
 */
function addImagePreview(imageUrl, filename) {
  const imagePreviewList = $('#imagePreviewList');
  if (!imagePreviewList) return;

  const previewItem = createElement('div', { className: 'image-preview-item' });

  const img = createElement('img', {
    src: getThumbnailUrl(imageUrl, 150),
    alt: filename,
    className: 'preview-image'
  });

  const info = createElement('div', { className: 'preview-info' });
  const nameSpan = createElement('span', {
    className: 'preview-name',
    textContent: filename
  });

  const removeBtn = createElement('button', {
    type: 'button',
    className: 'preview-remove',
    innerHTML: '×',
    title: '移除圖片'
  });

  on(removeBtn, 'click', () => {
    // 從上傳列表中移除
    const index = uploadedImages.indexOf(imageUrl);
    if (index > -1) {
      uploadedImages.splice(index, 1);
    }
    // 移除預覽元素
    previewItem.remove();
  });

  info.appendChild(nameSpan);
  info.appendChild(removeBtn);
  previewItem.appendChild(img);
  previewItem.appendChild(info);
  imagePreviewList.appendChild(previewItem);
}

/**
 * 清空圖片預覽
 */
function clearImagePreviews() {
  const imagePreviewList = $('#imagePreviewList');
  if (imagePreviewList) {
    imagePreviewList.innerHTML = '';
  }
  uploadedImages = [];
}

// 頁面載入完成後初始化
ready(() => {
  init();
  initImageUpload();
});
